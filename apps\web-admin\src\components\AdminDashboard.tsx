'use client';

import { useState } from 'react';
import { Shield, User, LogOut, Settings, Bell, Home } from 'lucide-react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useCurrentAdmin } from '@/components/auth/AuthGuard';
import { payloadAuth } from '@/lib/payload-auth';

interface DashboardStats {
  totalContacts: number;
  newContacts: number;
  totalSubscribers: number;
  activeSubscribers: number;
}

interface AdminDashboardProps {
  children?: React.ReactNode;
  showHeader?: boolean;
}

export function AdminDashboard({ children, showHeader = true }: AdminDashboardProps) {
  const adminUser = useCurrentAdmin();

  const handleSignOut = async () => {
    try {
      await payloadAuth.logout();
      window.location.href = '/admin/login';
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };
  const pathname = usePathname();
  const [stats] = useState<DashboardStats>({
    totalContacts: 42,
    newContacts: 7,
    totalSubscribers: 156,
    activeSubscribers: 134,
  });

  // If children are provided, render them with optional header
  if (children) {
    return (
      <div className="min-h-screen bg-gray-50">
        {showHeader && adminUser && (
          <AdminHeader user={adminUser} onSignOut={handleSignOut} />
        )}
        <div className="flex">
          <AdminSidebar currentPath={pathname} />
          <main className="flex-1">
            {children}
          </main>
        </div>
      </div>
    );
  }

  // Default dashboard content
  return (
    <div className="min-h-screen bg-gray-50">
      {adminUser && <AdminHeader user={adminUser} onSignOut={handleSignOut} />}

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Contacts</h3>
            <p className="text-2xl font-bold text-gray-900">{stats.totalContacts}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">New Contacts</h3>
            <p className="text-2xl font-bold text-blue-600">{stats.newContacts}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Total Subscribers</h3>
            <p className="text-2xl font-bold text-gray-900">{stats.totalSubscribers}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-sm font-medium text-gray-500">Active Subscribers</h3>
            <p className="text-2xl font-bold text-green-600">{stats.activeSubscribers}</p>
          </div>
        </div>

        {/* Welcome Message */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-gray-900 mb-4">
            Welcome to Admin Dashboard
          </h2>
          <p className="text-gray-600">
            You are now authenticated and have access to the admin control panel.
          </p>
        </div>
      </div>
    </div>
  );
}

// Admin Header Component
interface AdminHeaderProps {
  user: {
    id: number;
    email: string;
    firstName: string;
    lastName: string;
    role: 'super-admin' | 'admin' | 'editor' | 'viewer';
    isActive: boolean;
  };
  onSignOut: () => void;
}

function AdminHeader({ user, onSignOut }: AdminHeaderProps) {
  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
              <Shield className="w-5 h-5 text-white" />
            </div>
            <h1 className="text-xl font-bold text-gray-900">Encreasl Admin</h1>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
              <Bell className="w-5 h-5" />
            </button>

            {/* Settings */}
            <button className="p-2 text-gray-400 hover:text-gray-600 transition-colors">
              <Settings className="w-5 h-5" />
            </button>

            {/* User Info */}
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900">
                {user.firstName} {user.lastName}
              </p>
              <p className="text-xs text-gray-500">
                {user.role === 'super-admin' ? 'Super Admin' : user.role.charAt(0).toUpperCase() + user.role.slice(1)}
              </p>
            </div>

            {/* Avatar */}
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
              <User className="w-4 h-4 text-gray-600" />
            </div>

            {/* Sign Out */}
            <button
              onClick={onSignOut}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Sign Out"
            >
              <LogOut className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </header>
  );
}

// Admin Sidebar Component
interface AdminSidebarProps {
  currentPath: string;
}

function AdminSidebar({ currentPath }: AdminSidebarProps) {
  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/admin',
      icon: Home,
      current: currentPath === '/admin',
    },
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: Settings,
      current: currentPath.startsWith('/admin/settings'),
    },
  ];

  return (
    <div className="w-64 bg-white shadow-sm border-r border-gray-200">
      <nav className="mt-5 px-2">
        <div className="space-y-1">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <Link
                key={item.name}
                href={item.href}
                className={`
                  group flex items-center px-2 py-2 text-sm font-medium rounded-md
                  ${item.current
                    ? 'bg-blue-100 text-blue-900'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                  }
                `}
              >
                <Icon
                  className={`
                    mr-3 h-5 w-5 flex-shrink-0
                    ${item.current
                      ? 'text-blue-500'
                      : 'text-gray-400 group-hover:text-gray-500'
                    }
                  `}
                />
                {item.name}
              </Link>
            );
          })}
        </div>
      </nav>
    </div>
  );
}


