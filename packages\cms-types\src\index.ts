/**
 * @encreasl/cms-types - Shared CMS Types Package
 * 
 * Provides TypeScript types and schemas for Payload CMS collections
 * shared across the Encreasl monorepo.
 */

// ========================================
// PAYLOAD CMS TYPES
// ========================================

// Re-export Payload types from the CMS app
// Note: These types should be generated by Payload CMS
export interface Post {
  id: string;
  title: string;
  slug: string;
  content?: any;
  excerpt?: string;
  featuredImage?: Media | string;
  status: 'draft' | 'published';
  publishedAt?: string;
  author: User | string;
  tags?: Array<{ tag: string }>;
  seo?: {
    title?: string;
    description?: string;
  };
  updatedAt: string;
  createdAt: string;
}

export interface Media {
  id: string;
  url?: string;
  filename: string;
  alt?: string;
  mimeType?: string;
  filesize?: number;
  updatedAt: string;
  createdAt: string;
}

export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  updatedAt: string;
  createdAt: string;
}

export interface Service {
  id: string;
  title: string;
  description?: string;
  updatedAt: string;
  createdAt: string;
}

export interface Config {
  collections: any[];
  globals: any[];
}

// ========================================
// CUSTOM TYPES
// ========================================

export interface PostFormData {
  title: string;
  slug: string;
  content: any; // Lexical editor state
  excerpt?: string;
  featuredImage?: string; // Media ID
  status: 'draft' | 'published';
  publishedAt?: string;
  author: string; // User ID
  tags?: Array<{ tag: string }>;
  seo?: {
    title?: string;
    description?: string;
  };
}

export interface MediaUploadData {
  file: File;
  alt: string;
}

export interface PostListItem {
  id: string;
  title: string;
  slug: string;
  status: 'draft' | 'published';
  publishedAt?: string;
  updatedAt: string;
  author: {
    id: string;
    email: string;
    firstName?: string;
    lastName?: string;
  };
  featuredImage?: {
    id: string;
    url: string;
    alt: string;
  };
}

export interface PostFilters {
  status?: 'draft' | 'published';
  author?: string;
  search?: string;
  limit?: number;
  page?: number;
}

// ========================================
// API RESPONSE TYPES
// ========================================

export interface CMSApiResponse<T = any> {
  docs?: T[];
  doc?: T;
  totalDocs?: number;
  limit?: number;
  page?: number;
  totalPages?: number;
  hasNextPage?: boolean;
  hasPrevPage?: boolean;
  errors?: Array<{
    message: string;
    field?: string;
  }>;
}

export interface CMSError {
  message: string;
  status?: number;
  field?: string;
}

// ========================================
// VALIDATION SCHEMAS
// ========================================

export * from './schemas';
export * from './api';
