/**
 * @file apps/web-admin/src/server/validators/admin-schemas.ts
 * @description Validation schemas for admin user management operations
 */

import { z } from 'zod';

// ========================================
// ADMIN USER SCHEMAS
// ========================================

/**
 * Admin user creation data validation schema
 */
export const CreateAdminUserSchema = z.object({
  email: z
    .string()
    .email('Invalid email address')
    .max(254, 'Email address too long')
    .transform(val => val.toLowerCase().trim()),
  
  firstName: z
    .string()
    .min(1, 'First name is required')
    .max(50, 'First name too long')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'First name contains invalid characters')
    .transform(val => val.trim()),
  
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .max(50, 'Last name too long')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'Last name contains invalid characters')
    .transform(val => val.trim()),
  
  roles: z
    .array(z.enum(['super_admin', 'admin', 'moderator', 'support', 'readonly']))
    .min(1, 'At least one role is required')
    .max(5, 'Too many roles assigned'),
  
  permissions: z
    .array(z.string().max(100))
    .max(100, 'Too many permissions')
    .optional()
    .default([]),
  
  status: z
    .enum(['active', 'inactive', 'suspended', 'pending_activation'])
    .default('pending_activation'),
  
  department: z
    .enum(['engineering', 'support', 'marketing', 'sales', 'operations', 'security'])
    .optional(),
  
  jobTitle: z
    .string()
    .max(100, 'Job title too long')
    .optional()
    .transform(val => val?.trim() || undefined),
  
  phone: z
    .string()
    .regex(/^[+]?[1-9][\d\s\-()]{0,15}$/, 'Invalid phone number format')
    .optional()
    .transform(val => val?.replace(/[\s\-()]/g, '') || undefined),
  
  // Security settings
  twoFactorRequired: z
    .boolean()
    .optional()
    .default(true), // Require 2FA for admin users by default
  
  sessionTimeout: z
    .number()
    .int('Session timeout must be an integer')
    .min(5, 'Session timeout must be at least 5 minutes')
    .max(480, 'Session timeout cannot exceed 8 hours')
    .optional()
    .default(60), // 1 hour default
  
  ipWhitelist: z
    .array(z.string().regex(/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, 'Invalid IP address'))
    .max(10, 'Too many IP addresses in whitelist')
    .optional()
    .default([]),
  
  // Admin metadata
  adminNotes: z
    .string()
    .max(1000, 'Admin notes too long')
    .optional()
    .transform(val => val?.trim() || undefined),
  
  // Temporary access
  accessExpiresAt: z
    .string()
    .datetime()
    .optional(),
  
  // Notification preferences
  emailNotifications: z
    .boolean()
    .optional()
    .default(true),
  
  slackNotifications: z
    .boolean()
    .optional()
    .default(false),
});

/**
 * Admin user update data validation schema
 */
export const UpdateAdminUserSchema = CreateAdminUserSchema.partial().extend({
  id: z.string().uuid('Invalid admin user ID'),
  
  // Prevent email changes for security
  email: z
    .string()
    .email('Invalid email address')
    .max(254, 'Email address too long')
    .transform(val => val.toLowerCase().trim())
    .optional(),
  
  // Track who made the update
  lastModifiedBy: z
    .string()
    .uuid('Invalid admin ID')
    .optional(),
  
  // Password reset flag
  requirePasswordReset: z
    .boolean()
    .optional(),
});

/**
 * Admin user deletion schema
 */
export const DeleteAdminUserSchema = z.object({
  id: z.string().uuid('Invalid admin user ID'),
  reason: z
    .enum(['resignation', 'termination', 'role_change', 'security_breach', 'other'])
    .default('other'),
  transferResponsibilitiesTo: z
    .string()
    .uuid('Invalid admin ID')
    .optional(),
  adminNotes: z
    .string()
    .max(1000, 'Admin notes too long')
    .optional(),
  revokeAllSessions: z
    .boolean()
    .optional()
    .default(true),
});

// ========================================
// ROLE MANAGEMENT SCHEMAS
// ========================================

/**
 * Role assignment schema
 */
export const AssignRoleSchema = z.object({
  adminId: z.string().uuid('Invalid admin user ID'),
  roles: z
    .array(z.enum(['super_admin', 'admin', 'moderator', 'support', 'readonly']))
    .min(1, 'At least one role is required')
    .max(5, 'Too many roles'),
  reason: z
    .string()
    .max(500, 'Reason too long')
    .optional(),
  expiresAt: z
    .string()
    .datetime()
    .optional(),
});

/**
 * Role revocation schema
 */
export const RevokeRoleSchema = z.object({
  adminId: z.string().uuid('Invalid admin user ID'),
  roles: z
    .array(z.enum(['super_admin', 'admin', 'moderator', 'support', 'readonly']))
    .min(1, 'At least one role is required'),
  reason: z
    .string()
    .max(500, 'Reason too long')
    .optional(),
});

/**
 * Permission assignment schema
 */
export const AssignPermissionSchema = z.object({
  adminId: z.string().uuid('Invalid admin user ID'),
  permissions: z
    .array(z.string().max(100))
    .min(1, 'At least one permission is required')
    .max(50, 'Too many permissions'),
  reason: z
    .string()
    .max(500, 'Reason too long')
    .optional(),
  expiresAt: z
    .string()
    .datetime()
    .optional(),
});

// ========================================
// ADMIN SESSION SCHEMAS
// ========================================

/**
 * Admin session validation schema
 */
export const AdminSessionSchema = z.object({
  adminId: z.string().uuid('Invalid admin user ID'),
  sessionToken: z
    .string()
    .min(32, 'Session token too short')
    .max(256, 'Session token too long'),
  ipAddress: z
    .string()
    .regex(/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, 'Invalid IP address'),
  userAgent: z
    .string()
    .max(500, 'User agent too long'),
  expiresAt: z
    .string()
    .datetime(),
  lastActivityAt: z
    .string()
    .datetime(),
});

/**
 * Admin session termination schema
 */
export const TerminateSessionSchema = z.object({
  sessionId: z.string().uuid('Invalid session ID'),
  reason: z
    .enum(['logout', 'timeout', 'security', 'admin_action'])
    .default('logout'),
  terminatedBy: z
    .string()
    .uuid('Invalid admin ID')
    .optional(),
});

// ========================================
// ADMIN ACTIVITY SCHEMAS
// ========================================

/**
 * Admin activity log schema
 */
export const AdminActivitySchema = z.object({
  adminId: z.string().uuid('Invalid admin user ID'),
  action: z
    .string()
    .max(100, 'Action name too long'),
  resource: z
    .string()
    .max(100, 'Resource name too long')
    .optional(),
  resourceId: z
    .string()
    .max(100, 'Resource ID too long')
    .optional(),
  details: z
    .record(z.string(), z.unknown())
    .optional(),
  ipAddress: z
    .string()
    .regex(/^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/, 'Invalid IP address'),
  userAgent: z
    .string()
    .max(500, 'User agent too long'),
  timestamp: z
    .string()
    .datetime(),
  severity: z
    .enum(['low', 'medium', 'high', 'critical'])
    .default('medium'),
});

// ========================================
// ADMIN PREFERENCES SCHEMAS
// ========================================

/**
 * Admin preferences schema
 */
export const AdminPreferencesSchema = z.object({
  adminId: z.string().uuid('Invalid admin user ID'),
  theme: z
    .enum(['light', 'dark', 'auto'])
    .default('auto'),
  language: z
    .string()
    .length(2, 'Language code must be 2 characters')
    .regex(/^[a-z]{2}$/, 'Invalid language code')
    .default('en'),
  timezone: z
    .string()
    .max(50, 'Timezone too long')
    .default('UTC'),
  dateFormat: z
    .enum(['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'])
    .default('MM/DD/YYYY'),
  timeFormat: z
    .enum(['12h', '24h'])
    .default('12h'),
  dashboardLayout: z
    .record(z.string(), z.unknown())
    .optional(),
  notifications: z.object({
    email: z.boolean().default(true),
    browser: z.boolean().default(true),
    slack: z.boolean().default(false),
    sms: z.boolean().default(false),
  }).optional(),
});

// ========================================
// TYPE EXPORTS
// ========================================

export type CreateAdminUserData = z.infer<typeof CreateAdminUserSchema>;
export type UpdateAdminUserData = z.infer<typeof UpdateAdminUserSchema>;
export type DeleteAdminUserData = z.infer<typeof DeleteAdminUserSchema>;
export type AssignRoleData = z.infer<typeof AssignRoleSchema>;
export type RevokeRoleData = z.infer<typeof RevokeRoleSchema>;
export type AssignPermissionData = z.infer<typeof AssignPermissionSchema>;
export type AdminSessionData = z.infer<typeof AdminSessionSchema>;
export type TerminateSessionData = z.infer<typeof TerminateSessionSchema>;
export type AdminActivityData = z.infer<typeof AdminActivitySchema>;
export type AdminPreferencesData = z.infer<typeof AdminPreferencesSchema>;

// ========================================
// VALIDATION FUNCTIONS
// ========================================

/**
 * Validate admin user creation data
 */
export function validateCreateAdminUser(data: unknown) {
  return CreateAdminUserSchema.safeParse(data);
}

/**
 * Validate admin user update data
 */
export function validateUpdateAdminUser(data: unknown) {
  return UpdateAdminUserSchema.safeParse(data);
}

/**
 * Validate role assignment data
 */
export function validateAssignRole(data: unknown) {
  return AssignRoleSchema.safeParse(data);
}

/**
 * Validate admin session data
 */
export function validateAdminSession(data: unknown) {
  return AdminSessionSchema.safeParse(data);
}

/**
 * Validate admin preferences data
 */
export function validateAdminPreferences(data: unknown) {
  return AdminPreferencesSchema.safeParse(data);
}

// ========================================
// CUSTOM VALIDATORS
// ========================================

/**
 * Super admin email domain validator
 */
export const superAdminEmailValidator = z
  .string()
  .email()
  .refine(
    (email) => {
      const allowedDomains = process.env.ADMIN_ALLOWED_DOMAINS?.split(',') || [];
      if (allowedDomains.length === 0) return true;
      
      const domain = email.split('@')[1];
      return allowedDomains.includes(domain);
    },
    'Email domain not allowed for admin users'
  );

/**
 * Role hierarchy validator
 */
export const roleHierarchyValidator = z
  .array(z.string())
  .refine(
    (roles) => {
      // Ensure role hierarchy is respected
      const hierarchy = ['readonly', 'support', 'moderator', 'admin', 'super_admin'];
      const roleIndices = roles.map(role => hierarchy.indexOf(role));
      
      // Check if all roles are valid
      return roleIndices.every(index => index !== -1);
    },
    'Invalid role in hierarchy'
  );

// ========================================
// SCHEMA TRANSFORMERS
// ========================================

/**
 * Transform admin user data for storage
 */
export const AdminUserStorageSchema = CreateAdminUserSchema.transform((data) => ({
  ...data,
  id: crypto.randomUUID(),
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  lastLoginAt: null,
  loginCount: 0,
  failedLoginAttempts: 0,
  lastFailedLoginAt: null,
  isDeleted: false,
  activationToken: crypto.randomUUID(),
  activatedAt: null,
}));

/**
 * Transform admin user data for API response (remove sensitive fields)
 */
export const AdminUserApiResponseSchema = AdminUserStorageSchema.transform((data) => {
  const {
    adminNotes: _adminNotes,
    activationToken: _activationToken,
    failedLoginAttempts: _failedLoginAttempts,
    lastFailedLoginAt: _lastFailedLoginAt,
    ipWhitelist: _ipWhitelist,
    ...publicData
  } = data;
  return publicData;
});
