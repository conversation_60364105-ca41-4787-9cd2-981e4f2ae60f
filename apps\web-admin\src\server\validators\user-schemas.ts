/**
 * @file apps/web-admin/src/server/validators/user-schemas.ts
 * @description Validation schemas for user management operations
 */

import { z } from 'zod';

// ========================================
// USER MANAGEMENT SCHEMAS
// ========================================

/**
 * User creation data validation schema
 */
export const CreateUserSchema = z.object({
  email: z
    .string()
    .email('Invalid email address')
    .max(254, 'Email address too long')
    .transform(val => val.toLowerCase().trim()),
  
  firstName: z
    .string()
    .min(1, 'First name is required')
    .max(50, 'First name too long')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'First name contains invalid characters')
    .transform(val => val.trim()),
  
  lastName: z
    .string()
    .min(1, 'Last name is required')
    .max(50, 'Last name too long')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'Last name contains invalid characters')
    .transform(val => val.trim()),
  
  role: z
    .enum(['user', 'premium', 'enterprise', 'trial'])
    .default('user'),
  
  status: z
    .enum(['active', 'inactive', 'pending', 'suspended', 'banned'])
    .default('pending'),
  
  phone: z
    .string()
    .regex(/^[+]?[1-9][\d\s\-()]{0,15}$/, 'Invalid phone number format')
    .optional()
    .transform(val => val?.replace(/[\s\-()]/g, '') || undefined),
  
  company: z
    .string()
    .max(100, 'Company name too long')
    .optional()
    .transform(val => val?.trim() || undefined),
  
  department: z
    .string()
    .max(50, 'Department name too long')
    .optional()
    .transform(val => val?.trim() || undefined),
  
  jobTitle: z
    .string()
    .max(100, 'Job title too long')
    .optional()
    .transform(val => val?.trim() || undefined),
  
  permissions: z
    .array(z.string().max(100))
    .max(50, 'Too many permissions')
    .optional()
    .default([]),
  
  tags: z
    .array(z.string().max(50))
    .max(20, 'Too many tags')
    .optional()
    .default([]),
  
  metadata: z
    .record(z.string(), z.unknown())
    .optional()
    .default({}),
  
  // Admin notes
  adminNotes: z
    .string()
    .max(1000, 'Admin notes too long')
    .optional()
    .transform(val => val?.trim() || undefined),
  
  // Email verification
  emailVerified: z
    .boolean()
    .optional()
    .default(false),
  
  // Account settings
  twoFactorEnabled: z
    .boolean()
    .optional()
    .default(false),
  
  marketingConsent: z
    .boolean()
    .optional()
    .default(false),
});

/**
 * User update data validation schema
 */
export const UpdateUserSchema = CreateUserSchema.partial().extend({
  id: z.string().uuid('Invalid user ID'),
  
  // Prevent certain fields from being updated
  email: z
    .string()
    .email('Invalid email address')
    .max(254, 'Email address too long')
    .transform(val => val.toLowerCase().trim())
    .optional(),
  
  // Update timestamp
  lastModifiedBy: z
    .string()
    .uuid('Invalid admin ID')
    .optional(),
});

/**
 * User deletion schema
 */
export const DeleteUserSchema = z.object({
  id: z.string().uuid('Invalid user ID'),
  reason: z
    .enum(['user_request', 'policy_violation', 'inactive', 'duplicate', 'other'])
    .default('other'),
  adminNotes: z
    .string()
    .max(500, 'Admin notes too long')
    .optional(),
  hardDelete: z
    .boolean()
    .optional()
    .default(false), // Soft delete by default
});

/**
 * User ban/suspension schema
 */
export const BanUserSchema = z.object({
  id: z.string().uuid('Invalid user ID'),
  reason: z
    .enum([
      'spam',
      'harassment',
      'policy_violation',
      'security_breach',
      'payment_fraud',
      'other'
    ]),
  duration: z
    .enum(['temporary', 'permanent'])
    .default('temporary'),
  expiresAt: z
    .string()
    .datetime()
    .optional(),
  adminNotes: z
    .string()
    .max(1000, 'Admin notes too long')
    .optional(),
  notifyUser: z
    .boolean()
    .optional()
    .default(true),
});

/**
 * User unban schema
 */
export const UnbanUserSchema = z.object({
  id: z.string().uuid('Invalid user ID'),
  reason: z
    .string()
    .max(500, 'Reason too long')
    .optional(),
  adminNotes: z
    .string()
    .max(1000, 'Admin notes too long')
    .optional(),
  notifyUser: z
    .boolean()
    .optional()
    .default(true),
});

// ========================================
// USER QUERY SCHEMAS
// ========================================

/**
 * User list filters schema
 */
export const UserListFiltersSchema = z.object({
  page: z
    .number()
    .int('Page must be an integer')
    .min(1, 'Page must be at least 1')
    .max(1000, 'Page number too high')
    .default(1),
  
  limit: z
    .number()
    .int('Limit must be an integer')
    .min(1, 'Limit must be at least 1')
    .max(100, 'Limit cannot exceed 100')
    .default(20),
  
  sortBy: z
    .enum(['createdAt', 'updatedAt', 'email', 'firstName', 'lastName', 'status'])
    .default('createdAt'),
  
  sortOrder: z
    .enum(['asc', 'desc'])
    .default('desc'),
  
  search: z
    .string()
    .max(200, 'Search query too long')
    .optional()
    .transform(val => val?.trim()),
  
  status: z
    .enum(['active', 'inactive', 'pending', 'suspended', 'banned'])
    .optional(),
  
  role: z
    .enum(['user', 'premium', 'enterprise', 'trial'])
    .optional(),
  
  emailVerified: z
    .boolean()
    .optional(),
  
  createdAfter: z
    .string()
    .datetime()
    .optional(),
  
  createdBefore: z
    .string()
    .datetime()
    .optional(),
  
  tags: z
    .array(z.string())
    .optional(),
});

/**
 * User search schema
 */
export const UserSearchSchema = z.object({
  query: z
    .string()
    .min(1, 'Search query cannot be empty')
    .max(200, 'Search query too long')
    .transform(val => val.trim()),
  
  fields: z
    .array(z.enum(['email', 'firstName', 'lastName', 'company', 'phone']))
    .optional()
    .default(['email', 'firstName', 'lastName']),
  
  exact: z
    .boolean()
    .optional()
    .default(false),
  
  ...UserListFiltersSchema.pick({
    page: true,
    limit: true,
    sortBy: true,
    sortOrder: true,
  }).shape,
});

// ========================================
// BULK OPERATIONS SCHEMAS
// ========================================

/**
 * Bulk user update schema
 */
export const BulkUpdateUsersSchema = z.object({
  userIds: z
    .array(z.string().uuid())
    .min(1, 'At least one user ID is required')
    .max(100, 'Cannot update more than 100 users at once'),
  
  updates: z.object({
    status: z.enum(['active', 'inactive', 'suspended']).optional(),
    role: z.enum(['user', 'premium', 'enterprise', 'trial']).optional(),
    tags: z.array(z.string().max(50)).max(20).optional(),
    permissions: z.array(z.string().max(100)).max(50).optional(),
  }),
  
  reason: z
    .string()
    .max(500, 'Reason too long')
    .optional(),
  
  notifyUsers: z
    .boolean()
    .optional()
    .default(false),
});

/**
 * Bulk user deletion schema
 */
export const BulkDeleteUsersSchema = z.object({
  userIds: z
    .array(z.string().uuid())
    .min(1, 'At least one user ID is required')
    .max(50, 'Cannot delete more than 50 users at once'),
  
  reason: z
    .enum(['user_request', 'policy_violation', 'inactive', 'duplicate', 'other'])
    .default('other'),
  
  adminNotes: z
    .string()
    .max(1000, 'Admin notes too long')
    .optional(),
  
  hardDelete: z
    .boolean()
    .optional()
    .default(false),
  
  notifyUsers: z
    .boolean()
    .optional()
    .default(true),
});

// ========================================
// TYPE EXPORTS
// ========================================

export type CreateUserData = z.infer<typeof CreateUserSchema>;
export type UpdateUserData = z.infer<typeof UpdateUserSchema>;
export type DeleteUserData = z.infer<typeof DeleteUserSchema>;
export type BanUserData = z.infer<typeof BanUserSchema>;
export type UnbanUserData = z.infer<typeof UnbanUserSchema>;
export type UserListFilters = z.infer<typeof UserListFiltersSchema>;
export type UserSearchData = z.infer<typeof UserSearchSchema>;
export type BulkUpdateUsersData = z.infer<typeof BulkUpdateUsersSchema>;
export type BulkDeleteUsersData = z.infer<typeof BulkDeleteUsersSchema>;

// ========================================
// VALIDATION FUNCTIONS
// ========================================

/**
 * Validate user creation data
 */
export function validateCreateUser(data: unknown) {
  return CreateUserSchema.safeParse(data);
}

/**
 * Validate user update data
 */
export function validateUpdateUser(data: unknown) {
  return UpdateUserSchema.safeParse(data);
}

/**
 * Validate user deletion data
 */
export function validateDeleteUser(data: unknown) {
  return DeleteUserSchema.safeParse(data);
}

/**
 * Validate user ban data
 */
export function validateBanUser(data: unknown) {
  return BanUserSchema.safeParse(data);
}

/**
 * Validate user list filters
 */
export function validateUserListFilters(data: unknown) {
  return UserListFiltersSchema.safeParse(data);
}

/**
 * Validate bulk user operations
 */
export function validateBulkUpdateUsers(data: unknown) {
  return BulkUpdateUsersSchema.safeParse(data);
}

// ========================================
// CUSTOM VALIDATORS
// ========================================

/**
 * Email domain validation for enterprise users
 */
export const enterpriseEmailValidator = z
  .string()
  .email()
  .refine(
    (email) => {
      const domain = email.split('@')[1];
      const personalDomains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
      return !personalDomains.includes(domain);
    },
    'Enterprise users must use a business email address'
  );

/**
 * Strong password validator for admin-created users
 */
export const strongPasswordValidator = z
  .string()
  .min(12, 'Password must be at least 12 characters')
  .max(128, 'Password too long')
  .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/, 
    'Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character');

// ========================================
// SCHEMA TRANSFORMERS
// ========================================

/**
 * Transform user data for storage
 */
export const UserStorageSchema = CreateUserSchema.transform((data) => ({
  ...data,
  id: crypto.randomUUID(),
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  lastLoginAt: null,
  loginCount: 0,
  isDeleted: false,
}));

/**
 * Transform user data for API response (remove sensitive fields)
 */
export const UserApiResponseSchema = UserStorageSchema.transform((data) => {
  const { adminNotes: _adminNotes, metadata: _metadata, ...publicData } = data;
  return publicData;
});
