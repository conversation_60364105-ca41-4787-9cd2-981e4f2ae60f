'use client';

import React, { useCallback, useEffect, useMemo } from 'react';
import {
  $createParagraphNode,
  $getRoot,
  EditorState
} from 'lexical';
import { LexicalComposer } from '@lexical/react/LexicalComposer';
import { RichTextPlugin } from '@lexical/react/LexicalRichTextPlugin';
import { ContentEditable } from '@lexical/react/LexicalContentEditable';
import { HistoryPlugin } from '@lexical/react/LexicalHistoryPlugin';
import { AutoFocusPlugin } from '@lexical/react/LexicalAutoFocusPlugin';
import { LinkPlugin } from '@lexical/react/LexicalLinkPlugin';
import { ListPlugin } from '@lexical/react/LexicalListPlugin';
import { MarkdownShortcutPlugin } from '@lexical/react/LexicalMarkdownShortcutPlugin';
import { TRANSFORMERS } from '@lexical/markdown';
import { OnChangePlugin } from '@lexical/react/LexicalOnChangePlugin';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import LexicalErrorBoundary from '@lexical/react/LexicalErrorBoundary';

// Lexical nodes
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { TableCellNode, TableNode, TableRowNode } from '@lexical/table';
import { ListItemNode, ListNode } from '@lexical/list';
import { CodeHighlightNode, CodeNode } from '@lexical/code';
import { AutoLinkNode, LinkNode } from '@lexical/link';
import { HorizontalRuleNode } from '@lexical/react/LexicalHorizontalRuleNode';

import { ToolbarPlugin } from './editor/ToolbarPlugin';
import { AutoLinkPlugin } from './editor/AutoLinkPlugin';

interface RichTextEditorProps {
  value?: unknown;
  onChange?: (value: unknown) => void;
  placeholder?: string;
  className?: string;
}

// Editor configuration - moved outside component to prevent recreation
const createEditorConfig = () => ({
  namespace: 'EncreaslPostEditor',
  nodes: [
    HeadingNode,
    ListNode,
    ListItemNode,
    QuoteNode,
    CodeNode,
    CodeHighlightNode,
    TableNode,
    TableCellNode,
    TableRowNode,
    AutoLinkNode,
    LinkNode,
    HorizontalRuleNode,
  ],
  onError(error: Error) {
    console.error('Lexical Editor Error:', error);
  },
  theme: {
    root: 'editor-root',
    paragraph: 'editor-paragraph',
    heading: {
      h1: 'editor-heading-h1',
      h2: 'editor-heading-h2',
      h3: 'editor-heading-h3',
      h4: 'editor-heading-h4',
      h5: 'editor-heading-h5',
      h6: 'editor-heading-h6',
    },
    list: {
      nested: {
        listitem: 'editor-nested-listitem',
      },
      ol: 'editor-list-ol',
      ul: 'editor-list-ul',
      listitem: 'editor-listitem',
    },
    image: 'editor-image',
    link: 'editor-link',
    text: {
      bold: 'editor-text-bold',
      italic: 'editor-text-italic',
      overflowed: 'editor-text-overflowed',
      hashtag: 'editor-text-hashtag',
      underline: 'editor-text-underline',
      strikethrough: 'editor-text-strikethrough',
      underlineStrikethrough: 'editor-text-underlineStrikethrough',
      code: 'editor-text-code',
    },
    code: 'editor-code',
    codeHighlight: {
      atrule: 'editor-tokenAttr',
      attr: 'editor-tokenAttr',
      boolean: 'editor-tokenProperty',
      builtin: 'editor-tokenSelector',
      cdata: 'editor-tokenComment',
      char: 'editor-tokenSelector',
      class: 'editor-tokenFunction',
      'class-name': 'editor-tokenFunction',
      comment: 'editor-tokenComment',
      constant: 'editor-tokenProperty',
      deleted: 'editor-tokenProperty',
      doctype: 'editor-tokenComment',
      entity: 'editor-tokenOperator',
      function: 'editor-tokenFunction',
      important: 'editor-tokenVariable',
      inserted: 'editor-tokenSelector',
      keyword: 'editor-tokenAttr',
      namespace: 'editor-tokenVariable',
      number: 'editor-tokenProperty',
      operator: 'editor-tokenOperator',
      prolog: 'editor-tokenComment',
      property: 'editor-tokenProperty',
      punctuation: 'editor-tokenPunctuation',
      regex: 'editor-tokenVariable',
      selector: 'editor-tokenSelector',
      string: 'editor-tokenSelector',
      symbol: 'editor-tokenProperty',
      tag: 'editor-tokenProperty',
      url: 'editor-tokenOperator',
      variable: 'editor-tokenVariable',
    },
  },
});

// Plugin to handle editor state changes
function OnChangeStatePlugin({
  onChange
}: {
  onChange: (editorState: EditorState) => void
}) {
  return (
    <OnChangePlugin
      onChange={onChange}
    />
  );
}

// Plugin to set initial value
function InitialValuePlugin({ value }: { value?: unknown }) {
  const [editor] = useLexicalComposerContext();
  
  useEffect(() => {
    if (value && editor) {
      try {
        const editorState = editor.parseEditorState(value as any);
        editor.setEditorState(editorState);
      } catch (error) {
        console.warn('Failed to parse initial editor state:', error);
        // Set default content if parsing fails
        editor.update(() => {
          const root = $getRoot();
          if (root.isEmpty()) {
            const paragraph = $createParagraphNode();
            root.append(paragraph);
          }
        });
      }
    }
  }, [editor, value]);
  
  return null;
}

// Create a stable config outside the component
const EDITOR_CONFIG = createEditorConfig();

export function RichTextEditor({
  value,
  onChange,
  placeholder = "Start writing...",
  className = ""
}: RichTextEditorProps) {
  const handleChange = useCallback((editorState: EditorState) => {
    if (onChange) {
      // Convert editor state to JSON for storage
      const serializedState = JSON.stringify(editorState.toJSON());
      onChange(serializedState);
    }
  }, [onChange]);

  return (
    <div className={`relative border border-gray-300 rounded-lg overflow-hidden ${className}`}>
      <LexicalComposer key="stable-editor" initialConfig={EDITOR_CONFIG}>
        <div className="editor-container">
          <ToolbarPlugin />
          <div className="editor-inner">
            <RichTextPlugin
              contentEditable={
                <ContentEditable 
                  className="editor-input min-h-[300px] p-4 outline-none resize-none text-gray-900"
                  style={{ caretColor: '#1f2937' }}
                />
              }
              placeholder={
                <div className="editor-placeholder absolute top-16 left-4 text-gray-400 pointer-events-none">
                  {placeholder}
                </div>
              }
              ErrorBoundary={LexicalErrorBoundary}
            />
            <OnChangeStatePlugin onChange={handleChange} />
            <InitialValuePlugin value={value} />
            <HistoryPlugin />
            <AutoFocusPlugin />
            <LinkPlugin />
            <AutoLinkPlugin />
            <ListPlugin />
            <MarkdownShortcutPlugin transformers={TRANSFORMERS} />
          </div>
        </div>
      </LexicalComposer>
      
      <style jsx>{`
        .editor-container {
          background: #fff;
          position: relative;
          line-height: 20px;
          font-weight: 400;
          text-align: left;
          border-top-left-radius: 10px;
          border-top-right-radius: 10px;
        }
        
        .editor-inner {
          background: #fff;
          position: relative;
        }
        
        .editor-input {
          min-height: 300px;
          resize: none;
          font-size: 15px;
          position: relative;
          tab-size: 1;
          outline: 0;
          padding: 15px 10px;
          caret-color: rgb(5, 5, 5);
        }
        
        .editor-placeholder {
          color: #999;
          overflow: hidden;
          position: absolute;
          text-overflow: ellipsis;
          top: 15px;
          left: 10px;
          font-size: 15px;
          user-select: none;
          display: inline-block;
          pointer-events: none;
        }
        
        .editor-paragraph {
          margin: 0;
          margin-bottom: 8px;
          position: relative;
        }
        
        .editor-paragraph:last-child {
          margin-bottom: 0;
        }
        
        .editor-heading-h1 {
          font-size: 24px;
          color: rgb(5, 5, 5);
          font-weight: 400;
          margin: 0;
          margin-bottom: 12px;
          padding: 0;
        }
        
        .editor-heading-h2 {
          font-size: 20px;
          color: rgb(5, 5, 5);
          font-weight: 400;
          margin: 0;
          margin-bottom: 10px;
          padding: 0;
        }
        
        .editor-heading-h3 {
          font-size: 18px;
          color: rgb(5, 5, 5);
          font-weight: 400;
          margin: 0;
          margin-bottom: 8px;
          padding: 0;
        }
        
        .editor-text-bold {
          font-weight: bold;
        }
        
        .editor-text-italic {
          font-style: italic;
        }
        
        .editor-text-underline {
          text-decoration: underline;
        }
        
        .editor-text-strikethrough {
          text-decoration: line-through;
        }
        
        .editor-text-underlineStrikethrough {
          text-decoration: underline line-through;
        }
        
        .editor-text-code {
          background-color: rgb(240, 242, 245);
          padding: 1px 0.25rem;
          font-family: Menlo, Consolas, Monaco, monospace;
          font-size: 94%;
        }
        
        .editor-link {
          color: rgb(33, 111, 219);
          text-decoration: none;
        }
        
        .editor-link:hover {
          text-decoration: underline;
        }
        
        .editor-list-ol {
          padding: 0;
          margin: 0;
          margin-left: 16px;
        }
        
        .editor-list-ul {
          padding: 0;
          margin: 0;
          margin-left: 16px;
        }
        
        .editor-listitem {
          margin: 8px 32px 8px 32px;
        }
        
        .editor-nested-listitem {
          list-style-type: none;
        }
        
        .editor-code {
          background-color: rgb(240, 242, 245);
          font-family: Menlo, Consolas, Monaco, monospace;
          display: block;
          padding: 8px 8px 8px 52px;
          line-height: 1.53;
          font-size: 13px;
          margin: 0;
          margin-top: 8px;
          margin-bottom: 8px;
          tab-size: 2;
          /* white-space: pre; */
          overflow-x: auto;
          position: relative;
        }
      `}</style>
    </div>
  );
}
