'use client';

import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  $getSelection,
  $isRangeSelection,
  $createParagraphNode,
  FORMAT_ELEMENT_COMMAND,
  FORMAT_TEXT_COMMAND,
  REDO_COMMAND,
  UNDO_COMMAND,
  SELECTION_CHANGE_COMMAND,
} from 'lexical';
import {
  $createHeadingNode,
  $createQuoteNode,
  $isHeadingNode,
} from '@lexical/rich-text';
import {
  $setBlocksType,
} from '@lexical/selection';
import { $createCodeNode } from '@lexical/code';
import {
  INSERT_ORDERED_LIST_COMMAND,
  INSERT_UNORDERED_LIST_COMMAND,
  REMOVE_LIST_COMMAND,
  $isListNode,
  ListNode,
} from '@lexical/list';
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import { mergeRegister } from '@lexical/utils';
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Code,
  Quote,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Undo,
  Redo,
} from 'lucide-react';

const LowPriority = 1;

const supportedBlockTypes = new Set([
  'paragraph',
  'quote',
  'code',
  'h1',
  'h2',
  'h3',
  'h4',
  'h5',
  'h6',
  'ul',
  'ol',
]);

const blockTypeToBlockName = {
  code: 'Code Block',
  h1: 'Large Heading',
  h2: 'Medium Heading',
  h3: 'Small Heading',
  h4: 'Tiny Heading',
  h5: 'Tiny Heading',
  h6: 'Tiny Heading',
  ol: 'Numbered List',
  paragraph: 'Normal',
  quote: 'Quote',
  ul: 'Bulleted List',
};

function Divider() {
  return <div className="w-px h-6 bg-gray-300 mx-1" />;
}

function Select({
  onChange,
  className,
  options,
  value,
}: {
  onChange: (value: string) => void;
  className: string;
  options: string[];
  value: string;
}) {
  return (
    <select
      className={className}
      onChange={(e) => onChange(e.target.value)}
      value={value}
    >
      <option hidden={true} value="" />
      {options.map((option) => (
        <option key={option} value={option}>
          {blockTypeToBlockName[option as keyof typeof blockTypeToBlockName] || option}
        </option>
      ))}
    </select>
  );
}

export function ToolbarPlugin() {
  const [editor] = useLexicalComposerContext();
  const toolbarRef = useRef(null);
  const [canUndo] = useState(false);
  const [canRedo] = useState(false);
  const [blockType, setBlockType] = useState('paragraph');
  const [, setSelectedElementKey] = useState<string | null>(null);
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [isStrikethrough, setIsStrikethrough] = useState(false);
  const [isCode, setIsCode] = useState(false);

  const updateToolbar = useCallback(() => {
    const selection = $getSelection();
    if ($isRangeSelection(selection)) {
      const anchorNode = selection.anchor.getNode();
      const element =
        anchorNode.getKey() === 'root'
          ? anchorNode
          : anchorNode.getTopLevelElementOrThrow();
      const elementKey = element.getKey();
      const elementDOM = editor.getElementByKey(elementKey);
      if (elementDOM !== null) {
        setSelectedElementKey(elementKey);
        if ($isListNode(element)) {
          const parentList = $getNearestNodeOfType(anchorNode, ListNode as any);
          const type = parentList ? (parentList as { getTag(): string }).getTag() : (element as { getTag(): string }).getTag();
          setBlockType(type);
        } else {
          const type = $isHeadingNode(element)
            ? element.getTag()
            : element.getType();
          setBlockType(type);
        }
      }
      // Update text format
      setIsBold(selection.hasFormat('bold'));
      setIsItalic(selection.hasFormat('italic'));
      setIsUnderline(selection.hasFormat('underline'));
      setIsStrikethrough(selection.hasFormat('strikethrough'));
      setIsCode(selection.hasFormat('code'));
    }
  }, [editor]);

  useEffect(() => {
    return mergeRegister(
      editor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => {
          updateToolbar();
        });
      }),
      editor.registerCommand(
        SELECTION_CHANGE_COMMAND,
        () => {
          updateToolbar();
          return false;
        },
        LowPriority,
      ),
    );
  }, [editor, updateToolbar]);

  const formatParagraph = () => {
    if (blockType !== 'paragraph') {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, () => $createParagraphNode());
        }
      });
    }
  };

  const formatLargeHeading = () => {
    if (blockType !== 'h1') {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, () => $createHeadingNode('h1'));
        }
      });
    }
  };

  const formatSmallHeading = () => {
    if (blockType !== 'h2') {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, () => $createHeadingNode('h2'));
        }
      });
    }
  };

  const formatQuote = () => {
    if (blockType !== 'quote') {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, () => $createQuoteNode());
        }
      });
    }
  };

  const formatCode = () => {
    if (blockType !== 'code') {
      editor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection)) {
          $setBlocksType(selection, () => $createCodeNode());
        }
      });
    }
  };

  const formatBulletList = () => {
    if (blockType !== 'ul') {
      editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
    } else {
      editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
    }
  };

  const formatNumberedList = () => {
    if (blockType !== 'ol') {
      editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
    } else {
      editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined);
    }
  };

  return (
    <div
      className="toolbar flex items-center space-x-1 p-2 border-b border-gray-200 bg-gray-50"
      ref={toolbarRef}
    >
      <button
        disabled={!canUndo}
        onClick={() => {
          editor.dispatchCommand(UNDO_COMMAND, undefined);
        }}
        className="toolbar-item p-2 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
        aria-label="Undo"
      >
        <Undo className="w-4 h-4" />
      </button>
      <button
        disabled={!canRedo}
        onClick={() => {
          editor.dispatchCommand(REDO_COMMAND, undefined);
        }}
        className="toolbar-item p-2 rounded hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
        aria-label="Redo"
      >
        <Redo className="w-4 h-4" />
      </button>
      <Divider />
      {supportedBlockTypes.has(blockType) && (
        <>
          <Select
            className="toolbar-item px-2 py-1 border border-gray-300 rounded text-sm"
            onChange={(value) => {
              if (value === 'paragraph') {
                formatParagraph();
              } else if (value === 'h1') {
                formatLargeHeading();
              } else if (value === 'h2') {
                formatSmallHeading();
              } else if (value === 'quote') {
                formatQuote();
              } else if (value === 'code') {
                formatCode();
              }
            }}
            options={['paragraph', 'h1', 'h2', 'h3', 'quote', 'code']}
            value={blockType}
          />
          <Divider />
        </>
      )}
      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold');
        }}
        className={`toolbar-item p-2 rounded hover:bg-gray-200 ${
          isBold ? 'bg-gray-200' : ''
        }`}
        aria-label="Format Bold"
      >
        <Bold className="w-4 h-4" />
      </button>
      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'italic');
        }}
        className={`toolbar-item p-2 rounded hover:bg-gray-200 ${
          isItalic ? 'bg-gray-200' : ''
        }`}
        aria-label="Format Italics"
      >
        <Italic className="w-4 h-4" />
      </button>
      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'underline');
        }}
        className={`toolbar-item p-2 rounded hover:bg-gray-200 ${
          isUnderline ? 'bg-gray-200' : ''
        }`}
        aria-label="Format Underline"
      >
        <Underline className="w-4 h-4" />
      </button>
      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'strikethrough');
        }}
        className={`toolbar-item p-2 rounded hover:bg-gray-200 ${
          isStrikethrough ? 'bg-gray-200' : ''
        }`}
        aria-label="Format Strikethrough"
      >
        <Strikethrough className="w-4 h-4" />
      </button>
      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'code');
        }}
        className={`toolbar-item p-2 rounded hover:bg-gray-200 ${
          isCode ? 'bg-gray-200' : ''
        }`}
        aria-label="Insert Code"
      >
        <Code className="w-4 h-4" />
      </button>
      <Divider />
      <button
        onClick={formatBulletList}
        className={`toolbar-item p-2 rounded hover:bg-gray-200 ${
          blockType === 'ul' ? 'bg-gray-200' : ''
        }`}
        aria-label="Insert Unordered List"
      >
        <List className="w-4 h-4" />
      </button>
      <button
        onClick={formatNumberedList}
        className={`toolbar-item p-2 rounded hover:bg-gray-200 ${
          blockType === 'ol' ? 'bg-gray-200' : ''
        }`}
        aria-label="Insert Ordered List"
      >
        <ListOrdered className="w-4 h-4" />
      </button>
      <button
        onClick={formatQuote}
        className={`toolbar-item p-2 rounded hover:bg-gray-200 ${
          blockType === 'quote' ? 'bg-gray-200' : ''
        }`}
        aria-label="Insert Quote"
      >
        <Quote className="w-4 h-4" />
      </button>
      <Divider />
      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, 'left');
        }}
        className="toolbar-item p-2 rounded hover:bg-gray-200"
        aria-label="Left Align"
      >
        <AlignLeft className="w-4 h-4" />
      </button>
      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, 'center');
        }}
        className="toolbar-item p-2 rounded hover:bg-gray-200"
        aria-label="Center Align"
      >
        <AlignCenter className="w-4 h-4" />
      </button>
      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, 'right');
        }}
        className="toolbar-item p-2 rounded hover:bg-gray-200"
        aria-label="Right Align"
      >
        <AlignRight className="w-4 h-4" />
      </button>
      <button
        onClick={() => {
          editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, 'justify');
        }}
        className="toolbar-item p-2 rounded hover:bg-gray-200"
        aria-label="Justify Align"
      >
        <AlignJustify className="w-4 h-4" />
      </button>
    </div>
  );
}

function $getNearestNodeOfType<T>(
  node: unknown,
  klass: new (...args: unknown[]) => T,
): T | null {
  let parent = node as { getParent?: () => unknown } | null;
  while (parent != null) {
    if (parent instanceof klass) {
      return parent as T;
    }
    parent = parent.getParent?.() as { getParent?: () => unknown } | null;
  }
  return null;
}
