/**
 * @file apps/web-admin/src/server/validators/index.ts
 * @description Validation schema exports for the Web Admin App server
 */

// ========================================
// USER MANAGEMENT VALIDATORS
// ========================================

export {
  CreateUserSchema,
  UpdateUserSchema,
  DeleteUserSchema,
  BanUserSchema,
  UnbanUserSchema,
  UserListFiltersSchema,
  UserSearchSchema,
  BulkUpdateUsersSchema,
  BulkDeleteUsersSchema,
  UserStorageSchema,
  UserApiResponseSchema,
  validateCreateUser,
  validateUpdateUser,
  validateDeleteUser,
  validateBanUser,
  validateUserListFilters,
  validateBulkUpdateUsers,
  enterpriseEmailValidator,
  strongPasswordValidator,
  type CreateUserData,
  type UpdateUserData,
  type DeleteUserData,
  type BanUserData,
  type UnbanUserData,
  type UserListFilters,
  type UserSearchData,
  type BulkUpdateUsersData,
  type BulkDeleteUsersData,
} from './user-schemas';

// ========================================
// ADMIN USER MANAGEMENT VALIDATORS
// ========================================

export {
  CreateAdminUserSchema,
  UpdateAdminUserSchema,
  DeleteAdminUserSchema,
  AssignRoleSchema,
  RevokeRoleSchema,
  AssignPermissionSchema,
  AdminSessionSchema,
  TerminateSessionSchema,
  AdminActivitySchema,
  AdminPreferencesSchema,
  AdminUserStorageSchema,
  AdminUserApiResponseSchema,
  validateCreateAdminUser,
  validateUpdateAdminUser,
  validateAssignRole,
  validateAdminSession,
  validateAdminPreferences,
  superAdminEmailValidator,
  roleHierarchyValidator,
  type CreateAdminUserData,
  type UpdateAdminUserData,
  type DeleteAdminUserData,
  type AssignRoleData,
  type RevokeRoleData,
  type AssignPermissionData,
  type AdminSessionData,
  type TerminateSessionData,
  type AdminActivityData,
  type AdminPreferencesData,
} from './admin-schemas';

// ========================================
// AUDIT AND COMPLIANCE VALIDATORS
// ========================================

export {
  CreateAuditLogSchema,
  AuditLogFiltersSchema,
  ExportAuditLogsSchema,
  GenerateComplianceReportSchema,
  DataRetentionPolicySchema,
  AuditLogStorageSchema,
  AuditLogExportSchema,
  validateCreateAuditLog,
  validateAuditLogFilters,
  validateExportAuditLogs,
  validateGenerateComplianceReport,
  validateDataRetentionPolicy,
  auditDateRangeValidator,
  complianceRequirementValidator,
  type CreateAuditLogData,
  type AuditLogFilters,
  type ExportAuditLogsData,
  type GenerateComplianceReportData,
  type DataRetentionPolicyData,
} from './audit-schemas';

// ========================================
// COMMON ADMIN VALIDATORS
// ========================================

import { z } from 'zod';

/**
 * Admin ID validation schema
 */
export const AdminIdSchema = z
  .string()
  .uuid('Invalid admin user ID');

/**
 * Resource ID validation schema
 */
export const ResourceIdSchema = z
  .string()
  .min(1, 'Resource ID is required')
  .max(100, 'Resource ID too long');

/**
 * Admin action validation schema
 */
export const AdminActionSchema = z
  .string()
  .min(1, 'Action is required')
  .max(100, 'Action name too long')
  .regex(/^[a-zA-Z0-9_\.]+$/, 'Action name can only contain letters, numbers, dots, and underscores');

/**
 * Admin reason validation schema
 */
export const AdminReasonSchema = z
  .string()
  .min(1, 'Reason is required')
  .max(500, 'Reason too long')
  .transform(val => val.trim());

/**
 * Admin notes validation schema
 */
export const AdminNotesSchema = z
  .string()
  .max(1000, 'Admin notes too long')
  .optional()
  .transform(val => val?.trim() || undefined);

/**
 * Admin pagination schema
 */
export const AdminPaginationSchema = z.object({
  page: z
    .number()
    .int('Page must be an integer')
    .min(1, 'Page must be at least 1')
    .max(1000, 'Page number too high')
    .default(1),
  
  limit: z
    .number()
    .int('Limit must be an integer')
    .min(1, 'Limit must be at least 1')
    .max(100, 'Limit cannot exceed 100')
    .default(20),
  
  sortBy: z
    .string()
    .max(50, 'Sort field name too long')
    .optional(),
  
  sortOrder: z
    .enum(['asc', 'desc'])
    .default('desc'),
});

/**
 * Admin search schema
 */
export const AdminSearchSchema = z.object({
  query: z
    .string()
    .min(1, 'Search query cannot be empty')
    .max(200, 'Search query too long')
    .transform(val => val.trim()),
  
  fields: z
    .array(z.string().max(50))
    .max(10, 'Too many search fields')
    .optional(),
  
  exact: z
    .boolean()
    .optional()
    .default(false),
  
  caseSensitive: z
    .boolean()
    .optional()
    .default(false),
  
  ...AdminPaginationSchema.shape,
});

/**
 * Admin date range schema
 */
export const AdminDateRangeSchema = z.object({
  startDate: z
    .string()
    .datetime('Invalid start date format'),
  
  endDate: z
    .string()
    .datetime('Invalid end date format'),
}).refine(
  (data) => {
    const start = new Date(data.startDate);
    const end = new Date(data.endDate);
    return end > start;
  },
  'End date must be after start date'
);

/**
 * Admin bulk operation schema
 */
export const AdminBulkOperationSchema = z.object({
  ids: z
    .array(z.string().uuid())
    .min(1, 'At least one ID is required')
    .max(100, 'Cannot process more than 100 items at once'),
  
  operation: z
    .string()
    .min(1, 'Operation is required')
    .max(50, 'Operation name too long'),
  
  parameters: z
    .record(z.string(), z.unknown())
    .optional(),
  
  reason: AdminReasonSchema.optional(),
  
  dryRun: z
    .boolean()
    .optional()
    .default(false),
  
  confirmationToken: z
    .string()
    .min(32, 'Confirmation token required for bulk operations')
    .optional(),
});

// ========================================
// VALIDATION UTILITIES FOR ADMIN
// ========================================

import type { AdminValidationError } from '../middleware/validation-middleware';

/**
 * Generic admin validation function
 */
export function validateAdminSchema<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: boolean; data?: T; errors?: AdminValidationError[] } {
  try {
    const validatedData = schema.parse(data);
    return {
      success: true,
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: AdminValidationError[] = error.issues.map((err: z.ZodIssue) => ({
        field: err.path.join('.'),
        message: err.message,
        code: err.code,
      }));
      
      return {
        success: false,
        errors,
      };
    }
    
    return {
      success: false,
      errors: [{
        field: 'unknown',
        message: 'Admin validation failed',
        code: 'ADMIN_VALIDATION_ERROR',
      }],
    };
  }
}

/**
 * Validate multiple admin schemas at once
 */
export function validateMultipleAdminSchemas<T1, T2>(
  schema1: z.ZodSchema<T1>,
  data1: unknown,
  schema2: z.ZodSchema<T2>,
  data2: unknown
): { 
  success: boolean; 
  data?: { data1: T1; data2: T2 }; 
  errors?: AdminValidationError[] 
} {
  const result1 = validateAdminSchema(schema1, data1);
  const result2 = validateAdminSchema(schema2, data2);
  
  const allErrors = [
    ...(result1.errors || []),
    ...(result2.errors || []),
  ];
  
  if (allErrors.length > 0) {
    return {
      success: false,
      errors: allErrors,
    };
  }
  
  return {
    success: true,
    data: {
      data1: result1.data!,
      data2: result2.data!,
    },
  };
}

/**
 * Create a partial version of any admin schema
 */
export function createPartialAdminSchema<T extends z.ZodRawShape>(
  schema: z.ZodObject<T>
): z.ZodObject<{ [K in keyof T]: z.ZodOptional<T[K]> }> {
  return schema.partial();
}

/**
 * Create a pick version of any admin schema
 * TODO: Fix TypeScript issues with complex generic constraints
 */
// export function createPickAdminSchema<T extends z.ZodRawShape, K extends keyof T>(
//   schema: z.ZodObject<T>,
//   keys: K[]
// ): z.ZodObject<Pick<T, K>> {
//   return schema.pick(Object.fromEntries(keys.map(key => [key, true])) as Record<K, true>);
// }

/**
 * Create an omit version of any admin schema
 * TODO: Fix TypeScript issues with complex generic constraints
 */
// export function createOmitAdminSchema<T extends z.ZodRawShape, K extends keyof T>(
//   schema: z.ZodObject<T>,
//   keys: K[]
// ): z.ZodObject<Omit<T, K>> {
//   return schema.omit(Object.fromEntries(keys.map(key => [key, true])) as Record<K, true>);
// }

// ========================================
// ADMIN SCHEMA COMPOSITION HELPERS
// ========================================

/**
 * Merge two admin schemas together
 * TODO: Fix TypeScript issues with complex generic constraints
 */
// export function mergeAdminSchemas<T extends z.ZodRawShape, U extends z.ZodRawShape>(
//   schema1: z.ZodObject<T>,
//   schema2: z.ZodObject<U>
// ): z.ZodObject<T & U> {
//   return schema1.merge(schema2);
// }

/**
 * Extend an admin schema with additional fields
 * TODO: Fix TypeScript issues with complex generic constraints
 */
// export function extendAdminSchema<T extends z.ZodRawShape, U extends z.ZodRawShape>(
//   baseSchema: z.ZodObject<T>,
//   extension: U
// ): z.ZodObject<T & U> {
//   return baseSchema.extend(extension);
// }

/**
 * Create a discriminated union schema for admin operations
 * TODO: Fix TypeScript issues with Zod discriminated union types
 */
// export function createAdminDiscriminatedUnion<
//   Discriminator extends string,
//   Options extends readonly [z.ZodDiscriminatedUnionOption<Discriminator>, ...z.ZodDiscriminatedUnionOption<Discriminator>[]]
// >(
//   discriminator: Discriminator,
//   options: Options
// ): z.ZodDiscriminatedUnion<Discriminator, Options> {
//   return z.discriminatedUnion(discriminator, options);
// }

// ========================================
// TYPE EXPORTS FOR COMMON SCHEMAS
// ========================================

export type AdminId = z.infer<typeof AdminIdSchema>;
export type ResourceId = z.infer<typeof ResourceIdSchema>;
export type AdminAction = z.infer<typeof AdminActionSchema>;
export type AdminReason = z.infer<typeof AdminReasonSchema>;
export type AdminNotes = z.infer<typeof AdminNotesSchema>;
export type AdminPagination = z.infer<typeof AdminPaginationSchema>;
export type AdminSearch = z.infer<typeof AdminSearchSchema>;
export type AdminDateRange = z.infer<typeof AdminDateRangeSchema>;
export type AdminBulkOperation = z.infer<typeof AdminBulkOperationSchema>;
