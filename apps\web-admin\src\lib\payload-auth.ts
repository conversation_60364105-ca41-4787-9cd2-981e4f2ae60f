/**
 * Payload CMS Authentication for Web Admin
 * 
 * This handles authentication with Payload CMS using email/password
 */

import { env } from './env';

export interface PayloadUser {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  role: 'super-admin' | 'admin' | 'editor' | 'viewer';
  isActive: boolean;
  token?: string;
}

export interface PayloadAuthResponse {
  message?: string;
  user?: PayloadUser;
  token?: string;
  exp?: number;
}

export class PayloadAuthClient {
  private baseUrl: string;
  private token: string | null = null;

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl.replace(/\/$/, '');
    // Try to get token from localStorage
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('payload-token');
    }
  }

  /**
   * Login with email and password
   */
  async login(email: string, password: string): Promise<PayloadAuthResponse> {
    const response = await fetch(`${this.baseUrl}/api/users/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      throw new Error(`Login failed: ${response.statusText}`);
    }

    const data: PayloadAuthResponse = await response.json();
    
    if (data.token) {
      this.token = data.token;
      if (typeof window !== 'undefined') {
        localStorage.setItem('payload-token', data.token);
      }
    }

    return data;
  }

  /**
   * Logout
   */
  async logout(): Promise<void> {
    if (!this.token) return;

    try {
      await fetch(`${this.baseUrl}/api/users/logout`, {
        method: 'POST',
        headers: {
          'Authorization': `JWT ${this.token}`,
        },
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.token = null;
      if (typeof window !== 'undefined') {
        localStorage.removeItem('payload-token');
      }
    }
  }

  /**
   * Get current user
   */
  async me(): Promise<PayloadUser | null> {
    if (!this.token) return null;

    try {
      const response = await fetch(`${this.baseUrl}/api/users/me`, {
        headers: {
          'Authorization': `JWT ${this.token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token expired, clear it
          this.token = null;
          if (typeof window !== 'undefined') {
            localStorage.removeItem('payload-token');
          }
        }
        return null;
      }

      const data = await response.json();
      return data.user;
    } catch (error) {
      console.error('Me request failed:', error);
      return null;
    }
  }

  /**
   * Get authorization header for API requests
   */
  getAuthHeader(): Record<string, string> {
    if (!this.token) return {};
    return {
      'Authorization': `JWT ${this.token}`,
    };
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    return !!this.token;
  }

  /**
   * Get current token
   */
  getToken(): string | null {
    return this.token;
  }
}

// Create singleton instance
export const payloadAuth = new PayloadAuthClient(
  env.NEXT_PUBLIC_CMS_SERVER_URL || 'https://cms.encreasl.com'
);

/**
 * Hook for Payload authentication state
 */
export function usePayloadAuth() {
  // This would be implemented with React state management
  // For now, return the client
  return payloadAuth;
}
