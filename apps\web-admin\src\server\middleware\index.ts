/**
 * @file apps/web-admin/src/server/middleware/index.ts
 * @description Middleware exports for the Web Admin App server
 */

// ========================================
// ADMIN AUTHENTICATION MIDDLEWARE
// ========================================

export {
  withAdminAuth,
  withOptionalAdminAuth,
  withRequiredAdminAuth,
  withSuperAdminAuth,
  withRoleAuth,
  withPermissionAuth,
  withAdminErrorHandling,
  withAdminAuditLog,
  type AdminAuthContext,
  type AdminAuthRequirement,
} from './auth-middleware';

// ========================================
// ADMIN RATE LIMITING MIDDLEWARE
// ========================================

export {
  withAdminRateLimit,
  withAdminRateLimitResult,
  withUserManagementRateLimit,
  withAdminUserRateLimit,
  withAuditLogRateLimit,
  withDashboardRateLimit,
  withSystemSettingsRateLimit,
  withAdminIdRateLimit,
  withAdminIpRateLimit,
  withResourceRateLimit,
  withMultipleAdminRateLimits,
  type AdminRateLimitConfig,
  type RateLimitResult,
} from './rate-limit-middleware';

// ========================================
// ADMIN VALIDATION MIDDLEWARE
// ========================================

export {
  withAdminValidation,
  withAdminValidationResult,
  withMultipleAdminValidation,
  withConditionalAdminValidation,
  withAdminSanitization,
  withAdminStringSanitization,
  withPermissionValidation,
  validateAdminData,
  type AdminValidationError,
  type AdminValidationOptions,
  type AdminValidationResult,
} from './validation-middleware';

// ========================================
// COMPOSITE MIDDLEWARE HELPERS
// ========================================

/**
 * Compose multiple admin middleware functions
 */
export function composeAdminMiddleware<T extends unknown[], R>(
  ...middlewares: Array<(fn: (...args: T) => Promise<R>) => (...args: T) => Promise<R>>
) {
  return (fn: (...args: T) => Promise<R>) => {
    return middlewares.reduceRight((acc, middleware) => middleware(acc), fn);
  };
}

/**
 * Create an admin middleware pipeline
 */
export function createAdminPipeline<T extends unknown[], R>(
  middlewares: Array<(fn: (...args: T) => Promise<R>) => (...args: T) => Promise<R>>
) {
  return (fn: (...args: T) => Promise<R>) => {
    return composeAdminMiddleware(...middlewares)(fn);
  };
}

// ========================================
// COMMON ADMIN MIDDLEWARE COMBINATIONS
// ========================================
// TODO: Implement missing middleware functions
// All middleware stacks are temporarily disabled until dependencies are implemented

/**
 * Standard admin middleware stack for user management
 */
export function withUserManagementDefaults<T extends unknown[], R>(
  _actionName: string,
  _requiredPermissions: string[] = ['users.read']
) {
  return (fn: (...args: T) => Promise<R>) => fn;
}

// All middleware functions temporarily disabled until dependencies are implemented

export function withAdminUserDefaults<T extends unknown[], R>(
  _actionName: string,
  _requiredPermissions: string[] = ['admin.manage']
) {
  return (fn: (...args: T) => Promise<R>) => fn;
}

export function withAuditDefaults<T extends unknown[], R>(
  _actionName: string,
  _requiredPermissions: string[] = ['audit.read']
) {
  return (fn: (...args: T) => Promise<R>) => fn;
}

export function withDashboardDefaults<T extends unknown[], R>(
  _actionName: string,
  _requiredPermissions: string[] = ['dashboard.read']
) {
  return (fn: (...args: T) => Promise<R>) => fn;
}

export function withSystemSettingsDefaults<T extends unknown[], R>(
  _actionName: string
) {
  return (fn: (...args: T) => Promise<R>) => fn;
}

export function withHighSecurityDefaults<T extends unknown[], R>(
  _actionName: string,
  _requiredPermissions: string[]
) {
  return (fn: (...args: T) => Promise<R>) => fn;
}

export function withBulkOperationDefaults<T extends unknown[], R>(
  _actionName: string,
  _requiredPermissions: string[]
) {
  return (fn: (...args: T) => Promise<R>) => fn;
}

export function withReadOnlyDefaults<T extends unknown[], R>(
  _actionName: string,
  _requiredPermissions: string[]
) {
  return (fn: (...args: T) => Promise<R>) => fn;
}

export function withWriteOperationDefaults<T extends unknown[], R>(
  _actionName: string,
  _requiredPermissions: string[]
) {
  return (fn: (...args: T) => Promise<R>) => fn;
}

export function withCriticalOperationDefaults<T extends unknown[], R>(
  _actionName: string
) {
  return (fn: (...args: T) => Promise<R>) => fn;
}
